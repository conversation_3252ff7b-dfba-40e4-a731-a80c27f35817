{"name": "rapidrunway", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/vite": "^4.1.3", "@types/underscore": "^1.13.0", "@uidotdev/usehooks": "^2.4.1", "@vercel/analytics": "^1.5.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "fabric": "^6.6.5", "framer-motion": "^12.7.4", "localforage": "^1.10.0", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "next-themes": "^0.4.6", "npyjs": "^0.7.0", "onnxruntime-web": "^1.22.0", "posthog-js": "^1.239.0", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-image-crop": "^11.0.10", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.0", "redux-persist": "^6.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "underscore": "^1.13.7", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}