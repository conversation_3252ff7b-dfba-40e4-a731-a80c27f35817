import React, { useEffect } from "react";
import * as fabric from "fabric";
import { Button } from "@/components/ui/button";
import Lottie from "lottie-react";
import lottie from "@/constants/lottie";
import { useSAM } from "@/hooks/useSAM";

interface CanvasAreaProps {
    canvasRef: React.RefObject<HTMLCanvasElement | null>;
    canvasContainerRef: React.RefObject<HTMLDivElement | null>;
    samMaskCanvasRef: React.RefObject<HTMLCanvasElement | null>;
    originalImage: fabric.Image | null;
    isLoading: boolean;
    onFileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
    cursorCanvasRef: React.RefObject<HTMLCanvasElement | null>;
    sam: ReturnType<typeof useSAM>;
    selectedMode?: string;
    magicSubMode?: string;
}

export function CanvasArea({
    canvasRef,
    canvasContainerRef,
    samMaskCanvasRef,
    originalImage,
    isLoading,
    onFileUpload,
    cursorCanvasRef,
    sam,
    selectedMode,
    magicSubMode,
}: CanvasAreaProps) {
    // Effect to draw SAM mask overlay
    useEffect(() => {
        const maskCanvas = samMaskCanvasRef.current;
        const mainCanvas = canvasRef.current;
        if (!maskCanvas || !mainCanvas) return;

        const ctx = maskCanvas.getContext('2d');
        if (!ctx) return;

        // Set canvas dimensions to match main canvas exactly
        maskCanvas.width = mainCanvas.width;
        maskCanvas.height = mainCanvas.height;

        // Clear the canvas
        ctx.clearRect(0, 0, maskCanvas.width, maskCanvas.height);

        // Draw SAM mask if available
        // Prioritize hoverMaskImg if it exists and no clicks have been made
        const activeMask = sam.samState.clicks.length === 0 && sam.samState.hoverMaskImg ? sam.samState.hoverMaskImg : sam.samState.maskImg;

        if (activeMask && originalImage) {
            ctx.globalAlpha = 0.6;
            
            // Calculate the position and scale of the image within the canvas
            const imageLeft = originalImage.left || 0;
            const imageTop = originalImage.top || 0;
            const imageWidth = (originalImage.width || 0) * (originalImage.scaleX || 1);
            const imageHeight = (originalImage.height || 0) * (originalImage.scaleY || 1);
            
            // Draw the mask positioned and scaled to match the image
            ctx.drawImage(
                activeMask,
                imageLeft,
                imageTop,
                imageWidth,
                imageHeight
            );
            ctx.globalAlpha = 1.0;
        }
    }, [sam.samState.maskImg, sam.samState.hoverMaskImg, sam.samState.clicks, originalImage, samMaskCanvasRef, canvasRef]);

    return (
        <div className="w-full h-full flex flex-col">
            <div
                className="relative flex-grow flex justify-center items-center bg-gray-100 rounded-lg overflow-hidden"
                ref={canvasContainerRef}
                style={{ height: "calc(100% - 80px)" }}
            >
                <div className="canvas-wrapper relative flex justify-center items-center h-full w-full">
                    <div className="relative">
                        <canvas
                            ref={canvasRef}
                            className="max-h-full max-w-full object-contain"
                        />
                        
                        {/* SAM Mask Overlay Canvas */}
                        <canvas
                            ref={samMaskCanvasRef}
                            className="absolute top-0 left-0 pointer-events-none"
                            style={{
                                zIndex: 10,
                            }}
                        />
                    </div>
                    
                    <canvas
                        ref={cursorCanvasRef}
                        className="absolute inset-0 pointer-events-none"
                        style={{
                            width: '100%',
                            height: '100%',
                        }}
                    />

                    {/* Render SAM click dots */}
                    {selectedMode === "magic" && magicSubMode === "smart" && originalImage && sam.samState.clicks.map((click, index) => {
                        const canvasElement = canvasRef.current;
                        if (!canvasElement || !originalImage) return null;

                        // Calculate display position
                        const scaleX = canvasElement.width / (originalImage.width! * originalImage.scaleX!);
                        const scaleY = canvasElement.height / (originalImage.height! * originalImage.scaleY!);
                        const displayX = click.x * scaleX + (originalImage.left || 0);
                        const displayY = click.y * scaleY + (originalImage.top || 0);
                        const dotColor = click.clickType === 1 ? 'bg-green-500' : 'bg-red-500';
                        const dotSize = 8;

                        return (
                            <div
                                key={index}
                                className={`absolute rounded-full ${dotColor} pointer-events-none z-30 border-2 border-white`}
                                style={{
                                    width: `${dotSize}px`,
                                    height: `${dotSize}px`,
                                    left: `${displayX - dotSize / 2}px`,
                                    top: `${displayY - dotSize / 2}px`,
                                }}
                                title={`Click ${index + 1} (${click.clickType === 1 ? 'Positive' : 'Negative'})`}
                            />
                        );
                    })}

                    {!originalImage && (
                        <div className="absolute inset-0 flex flex-col items-center justify-center bg-white bg-opacity-90 space-y-4">
                            <p className="text-gray-500 text-lg">
                                Please upload an image to get started.
                            </p>
                            <input
                                id="canvas-file-upload"
                                type="file"
                                accept="image/*"
                                onChange={onFileUpload}
                                className="hidden"
                            />
                            <Button
                                asChild
                                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md font-medium"
                            >
                                <label
                                    htmlFor="canvas-file-upload"
                                    className="cursor-pointer"
                                >
                                    Upload Image
                                </label>
                            </Button>
                        </div>
                    )}
                    {isLoading && (
                        <div className="absolute inset-0 bg-black/30 rounded-lg flex items-center justify-center">
                            <Lottie
                                animationData={lottie.lottieLoader}
                                loop={true}
                                className="w-full h-full"
                            />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
