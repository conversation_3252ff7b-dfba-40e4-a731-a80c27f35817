import { useEffect, useRef, useState } from "react";
import * as fabric from "fabric";
import { useHistoryState } from "@uidotdev/usehooks";
import { editImage, removeBackground } from "@/lib/api";
import { toast } from "sonner";
import { Mode, MagicSubMode, UseCase } from "../EditTool";
import { generateMaskFromCanvas, createWhiteAnnotationPreviewFromCanvas } from "@/lib/imageUtils";
import { useSAM } from "@/hooks/useSAM";
import { SAMClick } from "@/types/sam";

export function useImageEditor(selectedMode: Mode, magicSubMode: MagicSubMode) {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const canvasContainerRef = useRef<HTMLDivElement>(null);
    const cursorCanvasRef = useRef<HTMLCanvasElement>(null);
    const samMaskCanvasRef = useRef<HTMLCanvasElement>(null); // For SAM mask overlay

    // SAM integration
    const sam = useSAM();

    // History of SAM clicks for undo/redo
    const clickHistoryRef = useRef<SAMClick[]>([]);
    const redoClicksRef = useRef<SAMClick[]>([]);

    const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
    const [originalImage, setOriginalImage] = useState<fabric.Image | null>(
        null
    );
    const [originalImageData, setOriginalImageData] =
        useState<ImageData | null>(null);
    const origCanvasRef = useRef<HTMLCanvasElement | null>(null);
    const [isCropping, setIsCropping] = useState<boolean>(false);
    const [cropRect, setCropRect] = useState<fabric.Rect | null>(null);
    const [brushSize, setBrushSize] = useState<number>(20);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [showMaskPreview, setShowMaskPreview] = useState<boolean>(false);
    const [originalImageUrl, setOriginalImageUrl] = useState<string | null>(
        null
    );
    const [annotatedImageUrl, setAnnotatedImageUrl] = useState<string | null>(
        null
    );
    const [hasPainted, setHasPainted] = useState<boolean>(false);

    const [initialState, setInitialState] = useState<string | null>(null);
    const {
        state: canvasState,
        set: setCanvasState,
        undo,
        redo,
        clear,
        canUndo: baseCanUndo,
        canRedo,
    } = useHistoryState<string | null>(null);

    // Undo smart select click: revert canvas and SAM clicks
    const undoWithSAMReset = () => {
        undo();
        // Pop last SAM click into redo stack
        const lastClick = clickHistoryRef.current.pop();
        if (lastClick) {
            redoClicksRef.current.push(lastClick);
            // Reset SAM selections and replay remaining clicks
            sam.resetClicks();
            clickHistoryRef.current.forEach((c) => {
                sam.handleClick(c.x, c.y);
            });
        }
    };

    // Redo smart select click: replay next SAM click
    const redoWithSAMAndPaint = () => {
        redo();
        if (selectedMode === "magic" && magicSubMode === "brush") {
            setHasPainted(true);
        }
        // Pop next click from redo stack and reapply
        const nextClick = redoClicksRef.current.pop();
        if (nextClick) {
            clickHistoryRef.current.push(nextClick);
            sam.handleClick(nextClick.x, nextClick.y);
        }
    };

    const canUndo = baseCanUndo && canvasState !== initialState;

    // State to hold the canvas state before cropping starts
    const [preCropState, setPreCropState] = useState<string | null>(null);

    const CANVAS_WIDTH = 800;
    const CANVAS_HEIGHT = 600;
    const brushColor = "rgba(102, 51, 153, 0.9)";

    const initializeCanvas = () => {
        if (!canvasRef.current) return;

        const containerWidth =
            canvasContainerRef.current?.offsetWidth || CANVAS_WIDTH;
        const containerHeight =
            canvasContainerRef.current?.offsetHeight || CANVAS_HEIGHT;
        if (canvas) canvas.dispose();
        try {
            const fabricCanvas = new fabric.Canvas(canvasRef.current, {
                width: containerWidth,
                height: containerHeight,
                backgroundColor: "#f0f0f0",
                isDrawingMode: selectedMode === "magic" && magicSubMode === "brush",
            });
            fabricCanvas.freeDrawingBrush = new fabric.PencilBrush(
                fabricCanvas
            );
            fabricCanvas.freeDrawingBrush.width = brushSize;
            fabricCanvas.freeDrawingBrush.color = brushColor;

            fabricCanvas.on("path:created", (e) => {
                if (e.path) {
                    setTimeout(() => {
                        const currentState = fabricCanvas.toDataURL({
                            multiplier: 1,
                            format: "png",
                            quality: 1,
                        });
                        setCanvasState(currentState);
                        setHasPainted(true);
                    }, 0);
                }
            });

            setCanvas(fabricCanvas);
        } catch (error) {
            console.error("Error initializing canvas:", error);
        }
    };

    useEffect(() => {
        const timer = setTimeout(initializeCanvas, 50);
        return () => {
            clearTimeout(timer);
            if (canvas) {
                canvas.dispose();
                setCanvas(null);
            }
        };
    }, []);

    useEffect(() => {
        if (canvas) {
            canvas.freeDrawingBrush!.width = brushSize;
        }
    }, [brushSize, canvas]);

    // Effect for drawing the custom brush cursor
    useEffect(() => {
        if (!canvas) return;

        const size = brushSize;
        const radius = size / 2;
        const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}"><circle cx="${radius}" cy="${radius}" r="${radius}" fill="rgba(102, 51, 153, 0.5)"/></svg>`;
        const url = `data:image/svg+xml;base64,${btoa(svg)}`;
        const cursorStyle = `url("${url}") ${radius} ${radius}, auto`;
        canvas.freeDrawingCursor = cursorStyle;
        const canvasEl = canvas.getElement();
        if (selectedMode === "magic" && magicSubMode === "brush") {
            canvasEl.style.cursor = cursorStyle;
        } else {
            canvas.freeDrawingCursor = "crosshair";
            canvasEl.style.cursor = "auto";
        }
    }, [canvas, brushSize, selectedMode, magicSubMode]);

    useEffect(() => {
        if (canvasState && canvas) {
            restoreCanvasState(canvasState);
        }
    }, [canvasState, canvas]);

    // Disable painting flag if state matches initial (no annotations)
    useEffect(() => {
        if (initialState && canvasState) {
            if (canvasState === initialState) {
                setHasPainted(false);
            }
        }
    }, [canvasState, initialState]);

    const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files?.[0] && canvas) {
            const file = e.target.files[0];
            const reader = new FileReader();
            reader.onload = async (event) => {
                if (event.target?.result) {
                    const dataUrl = event.target.result.toString();
                    const imgObj = new Image();
                    imgObj.src = dataUrl;
                    imgObj.onload = async () => {
                        // Reset SAM state before loading new image
                        sam.resetAll();
                        
                        canvas.clear();
                        const imgWidth = imgObj.width;
                        const imgHeight = imgObj.height;
                        const containerWidth =
                            canvasContainerRef.current?.offsetWidth ||
                            CANVAS_WIDTH;
                        const containerHeight =
                            canvasContainerRef.current?.offsetHeight ||
                            CANVAS_HEIGHT;
                        const widthRatio = containerWidth / imgWidth;
                        const heightRatio = containerHeight / imgHeight;
                        const scale = Math.min(widthRatio, heightRatio);
                        const newCanvasWidth = imgWidth * scale;
                        const newCanvasHeight = imgHeight * scale;

                        canvas.setWidth(newCanvasWidth);
                        canvas.setHeight(newCanvasHeight);
                        canvas.backgroundColor = "transparent";

                        const fabricImage = new fabric.FabricImage(imgObj, {
                            left: 0,
                            top: 0,
                            selectable: false,
                            evented: false,
                        });
                        fabricImage.scaleX = newCanvasWidth / imgWidth;
                        fabricImage.scaleY = newCanvasHeight / imgHeight;
                        canvas.add(fabricImage);
                        setOriginalImage(fabricImage);

                        const off = document.createElement("canvas");
                        off.width = newCanvasWidth;
                        off.height = newCanvasHeight;
                        const offCtx = off.getContext("2d")!;
                        offCtx.drawImage(
                            imgObj,
                            0,
                            0,
                            newCanvasWidth,
                            newCanvasHeight
                        );
                        origCanvasRef.current = off;
                        const scaledDataUrl = off.toDataURL("image/png");
                        setOriginalImageUrl(scaledDataUrl);
                        const imgData = offCtx.getImageData(
                            0,
                            0,
                            off.width,
                            off.height
                        );
                        setOriginalImageData(imgData);
                        setInitialState(
                            canvas.toDataURL({
                                multiplier: 1,
                                format: "png",
                                quality: 1,
                            })
                        );
                        setCanvasState(
                            canvas.toDataURL({
                                multiplier: 1,
                                format: "png",
                                quality: 1,
                            })
                        );
                        canvas.renderAll();

                        // SAM embedding is now manually triggered via button in MagicEdit component
                    };
                } else {
                    toast.error("Failed to read file.");
                }
            };
            reader.readAsDataURL(file);
            e.target.value = "";
        } else if (!canvas) {
             toast.error("Canvas is not initialized.");
        }
    };

    const testLog = async () => {
        if (!canvas || !originalImage) return;
        const maskImageDataURL = await generateMaskFromCanvas(canvas);

        const whiteAnnotationUrl = await createWhiteAnnotationPreviewFromCanvas(canvas, originalImage);
        console.log("Original Image URL:", originalImageUrl);
        console.log("Mask Image Data URL:", maskImageDataURL);
        console.log("White Annotation URL:", whiteAnnotationUrl);

    };

    const generateMaskAndSend = async (prompt: string, mode: MagicSubMode, useCase: UseCase) => {
        if (!canvas || !originalImage) return;
        setIsLoading(true);
        try {
            const whiteAnnotationUrl = await createWhiteAnnotationPreviewFromCanvas(canvas, originalImage);
            setAnnotatedImageUrl(whiteAnnotationUrl);

            const maskImageDataURL = await generateMaskFromCanvas(canvas);

            const promptMessage = prompt;
            console.log("Prompt Message:", promptMessage);
            console.log("Original Image URL:", originalImageUrl);
            console.log("Mask Image Data URL:", maskImageDataURL);
            console.log("White Annotation URL:", whiteAnnotationUrl);
            const response = await editImage({
                base64EncodedOriginalImage: originalImageUrl!,
                base64EncodedMaskImage: maskImageDataURL,
                base64EncodedMaskColorImage: whiteAnnotationUrl!,
                promptMessage: promptMessage,
                mode: mode,
                useCase: useCase,
            });
            console.log("Response Image:", response);

            const editedImageUrl =
                typeof response === "string" ? response : (response as string);
            setOriginalImageUrl(editedImageUrl);
            restoreCanvasState(editedImageUrl);
            if (initialState) {
                clear();
                setCanvasState(initialState);
                setCanvasState(editedImageUrl);
            }
            toast.success("Image edited successfully");
            setHasPainted(false);
        } catch (error) {
            console.error("Error generating or sending mask:", error);
            toast.error("Error editing image. See console for details.");
        } finally {
            setIsLoading(false);
        }
    };

    const generateRemoveBackground = async () => {
        if (!canvas || !originalImage) return;
        setIsLoading(true);
        try {
          const response = await removeBackground(originalImageUrl);
          const editedImageUrl =
              typeof response === "string" ? response : (response as string);
          setOriginalImageUrl(editedImageUrl);
          restoreCanvasState(editedImageUrl);
          if (initialState) {
              clear();
              setCanvasState(initialState);
              setCanvasState(editedImageUrl);
          }
          toast.success("Image edited successfully");
          setHasPainted(false);
        }catch (error) {
            console.error("Error generating or sending mask:", error);
            toast.error("Error editing image. See console for details.");
        } finally {
            setIsLoading(false);
        }
    };

    const clearBrushStrokes = () => {
        if (!canvas || !originalImage) return;
        canvas.getObjects().forEach((obj) => {
            if (obj.type === "path") {
                canvas.remove(obj);
            }
        });
        canvas.renderAll();
        const newState = canvas.toDataURL({
            multiplier: 1,
            format: "png",
            quality: 1,
        });
        setCanvasState(newState);
        setHasPainted(false);
    };

    const downloadOriginalImage = () => {
        if (!canvas || !originalImage) return;
        const originalDataUrl = canvas.toDataURL({
            multiplier: 1,
            format: "png",
            quality: 1,
        });
        const link = document.createElement("a");
        link.href = originalDataUrl;
        link.download = "original.png";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const restoreCanvasState = (stateDataURL: string) => {
        if (!canvas) return;
        const img = new Image();
        img.crossOrigin = "anonymous";
        img.onload = () => {
            // Compute scale to fit within container
            const imgWidth = img.width;
            const imgHeight = img.height;
            const containerWidth =
                canvasContainerRef.current?.offsetWidth || CANVAS_WIDTH;
            const containerHeight =
                canvasContainerRef.current?.offsetHeight || CANVAS_HEIGHT;
            const widthRatio = containerWidth / imgWidth;
            const heightRatio = containerHeight / imgHeight;
            const scale = Math.min(widthRatio, heightRatio);
            const newCanvasWidth = imgWidth * scale;
            const newCanvasHeight = imgHeight * scale;

            // Resize and clear canvas
            canvas.clear();
            canvas.setDimensions({ width: newCanvasWidth, height: newCanvasHeight });
            canvas.backgroundColor = "transparent";

            // Add and scale image - centered in the canvas
            const stateImg = new fabric.Image(img, {
                left: (newCanvasWidth - (imgWidth * scale)) / 2,
                top: (newCanvasHeight - (imgHeight * scale)) / 2,
                selectable: false,
                evented: false,
                opacity: 1,
            });
            stateImg.scaleX = scale;
            stateImg.scaleY = scale;
            canvas.add(stateImg);
            setOriginalImage(stateImg);
            canvas.renderAll();
        };
        img.src = stateDataURL;
    };

    const applyFillColor = (color: string) => {
        if (!canvas) return;
        const prevState = canvas.toDataURL({
            multiplier: 1,
            format: "png",
            quality: 1,
        });
        setCanvasState(prevState);
        const rect = new fabric.Rect({
            left: 0,
            top: 0,
            width: canvas.width!,
            height: canvas.height!,
            fill: color,
            selectable: false,
            evented: false,
        });
        if (showMaskPreview) {
            const imgEl = new Image();
            imgEl.crossOrigin = "anonymous";
            imgEl.onload = () => {
                const maskImg = new fabric.Image(imgEl, {
                    left: 0,
                    top: 0,
                    selectable: false,
                    evented: false,
                });
                rect.clipPath = maskImg;
                canvas.add(rect);
                canvas.renderAll();
                const newState = canvas.toDataURL({
                    multiplier: 1,
                    format: "png",
                    quality: 1,
                });
                setCanvasState(newState);
            };
            imgEl.src = originalImageUrl;
        } else {
            canvas.add(rect);
            canvas.renderAll();
            const newState = canvas.toDataURL({
                multiplier: 1,
                format: "png",
                quality: 1,
            });
            setCanvasState(newState);
        }
    };

    const applyPattern = (file: File) => {
        if (!canvas) return;
        const reader = new FileReader();
        reader.onload = (e) => {
            if (e.target?.result) {
                const dataUrl = e.target.result.toString();
                const imgEl = new Image();
                imgEl.crossOrigin = "anonymous";
                imgEl.onload = () => {
                    const patternSource = new fabric.Pattern({
                        source: imgEl,
                        repeat: "repeat",
                    });
                    const rect = new fabric.Rect({
                        left: 0,
                        top: 0,
                        width: canvas.width!,
                        height: canvas.height!,
                        fill: patternSource,
                        selectable: false,
                        evented: false,
                    });
                    if (showMaskPreview) {
                        const maskImg = new Image();
                        maskImg.crossOrigin = "anonymous";
                        maskImg.onload = () => {
                            const clipImg = new fabric.Image(maskImg, {
                                left: 0,
                                top: 0,
                                selectable: false,
                                evented: false,
                            });
                            rect.clipPath = clipImg;
                            canvas.add(rect);
                            canvas.renderAll();
                            const newState = canvas.toDataURL({
                                multiplier: 1,
                                format: "png",
                                quality: 1,
                            });
                            setCanvasState(newState);
                        };
                        maskImg.src = originalImageUrl;
                    } else {
                        canvas.add(rect);
                        canvas.renderAll();
                        const newState = canvas.toDataURL({
                            multiplier: 1,
                            format: "png",
                            quality: 1,
                        });
                        setCanvasState(newState);
                    }
                };
                imgEl.src = dataUrl;
            }
        };
        reader.readAsDataURL(file);
    };

    const applyMaterial = ({
        file,
        strength,
    }: {
        file: File;
        strength: number;
        guidance?: number;
    }) => {
        if (!canvas) return;
        applyPattern(file);
        canvas.getObjects().forEach((obj) => {
            if (obj.type === "rect") obj.opacity = strength;
        });
        canvas.renderAll();
        const newState = canvas.toDataURL({
            multiplier: 1,
            format: "png",
            quality: 1,
        });
        setCanvasState(newState);
    };

    const applyFilter = (key: string, value: number) => {
        if (!originalImageData) return;
        const data = new Uint8ClampedArray(originalImageData.data);
        const width = originalImageData.width;
        const height = originalImageData.height;
        for (let i = 0; i < data.length; i += 4) {
            let r = data[i],
                g = data[i + 1],
                b = data[i + 2];
            const a = data[i + 3];
            switch (key) {
                case "brightness": {
                    const delta = (value - 100) * 2.55;
                    r = Math.min(255, Math.max(0, r + delta));
                    g = Math.min(255, Math.max(0, g + delta));
                    b = Math.min(255, Math.max(0, b + delta));
                    break;
                }
                case "inversion": {
                    const t = value / 100;
                    r = r + t * (255 - 2 * r);
                    g = g + t * (255 - 2 * g);
                    b = b + t * (255 - 2 * b);
                    break;
                }
                case "grayscale": {
                    const t = value / 100;
                    const gray = 0.3 * r + 0.59 * g + 0.11 * b;
                    r = r + (gray - r) * t;
                    g = g + (gray - g) * t;
                    b = b + (gray - b) * t;
                    break;
                }
                case "saturation": {
                    const t = value / 100;
                    const avg = (r + g + b) / 3;
                    r = r + (r - avg) * t;
                    g = g + (g - avg) * t;
                    b = b + (b - avg) * t;
                    break;
                }
                case "temperature": {
                    const t = (value - 100) / 100;
                    r = Math.min(255, Math.max(0, r + 50 * t));
                    b = Math.min(255, Math.max(0, b - 50 * t));
                    break;
                }
                case "tint": {
                    const t = (value - 100) / 100;
                    g = Math.min(255, Math.max(0, g + 50 * t));
                    break;
                }
            }
            data[i] = r;
            data[i + 1] = g;
            data[i + 2] = b;
            data[i + 3] = a;
        }
        const newImg = new ImageData(data, width, height);
        updateCanvasFromImageData(newImg);
    };

    const hideMaskPreview = () => setShowMaskPreview(false);
    const toggleMaskPreview = () => setShowMaskPreview((prev) => !prev);

    const hasImage = Boolean(originalImage);

    const updateCanvasFromImageData = (imageData: ImageData) => {
        if (!canvas) return;
        const off = document.createElement("canvas");
        off.width = imageData.width;
        off.height = imageData.height;
        const ctx = off.getContext("2d")!;
        ctx.putImageData(imageData, 0, 0);
        const imgEl = new Image();
        imgEl.crossOrigin = "anonymous";
        imgEl.src = off.toDataURL();
        imgEl.onload = () => {
            canvas.clear();
            canvas.width = off.width;
            canvas.height = off.height;
            canvas.backgroundColor = "transparent";
            const fImg = new fabric.Image(imgEl, {
                left: 0,
                top: 0,
                selectable: false,
                evented: false,
            });
            canvas.add(fImg);
            setOriginalImage(fImg);
            canvas.renderAll();
            const state = canvas.toDataURL({
                multiplier: 1,
                format: "png",
                quality: 1,
            });
            setCanvasState(state);
        };
    };

    // reset to original loaded image (keep SAM selections)
    const resetImage = () => {
        if (!originalImageUrl) return;
        restoreCanvasState(originalImageUrl);
        setCanvasState(originalImageUrl);
        setHasPainted(false);
        // Don't reset SAM state - keep mask selections for further use
    };

    // NEW: Reset only SAM annotations but keep the current backend-edited image
    const resetSAMAnnotations = () => {
        // Only reset SAM clicks and UI state, preserve current image
        sam.resetClicks(); // Reset SAM selections but keep tensor for new selections
        setHasPainted(false);
        // Don't call resetImage() - this preserves backend-edited results
    };

    // cropping
    const startCrop = () => {
        if (!canvas || !originalImage) return;

        // Save current canvas state before adding the crop rectangle
        setPreCropState(canvas.toDataURL({
            multiplier: 1,
            format: "png",
            quality: 1,
        }));

        canvas.isDrawingMode = false;
        const imgWidth = originalImage.width! * originalImage.scaleX!;
        const imgHeight = originalImage.height! * originalImage.scaleY!;

        const rect = new fabric.Rect({
            left: 0,
            top: 0,
            width: imgWidth,
            height: imgHeight,
            fill: "rgba(0,0,0,0.3)",
            stroke: "white",
            strokeWidth: 2,
            strokeDashArray: [5, 5],
            selectable: true,
            evented: true,
            hasControls: true,
            lockUniScaling: true,
            cornerStrokeColor: "white",
            cornerStyle: "circle",
            cornerSize: 15,
            transparentCorners: false,
        });

        canvas.add(rect);
        canvas.setActiveObject(rect);
        setCropRect(rect);
        setIsCropping(true);

    };

    const confirmCrop = () => {
        if (!canvas || !cropRect || !origCanvasRef.current) return;
        const { left, top, width, height, scaleX, scaleY } = cropRect;
        const x = left;
        const y = top;
        const w = width * scaleX;
        const h = height * scaleY;

        const temp = document.createElement("canvas");
        temp.width = w;
        temp.height = h;
        const tCtx = temp.getContext("2d")!;
        tCtx.drawImage(origCanvasRef.current, x, y, w, h, 0, 0, w, h);

        const croppedImageDataURL = temp.toDataURL();

        const img = new Image();
        img.crossOrigin = "anonymous";
        img.onload = () => {
            const containerWidth =
                canvasContainerRef.current?.offsetWidth || CANVAS_WIDTH;
            const containerHeight =
                canvasContainerRef.current?.offsetHeight || CANVAS_HEIGHT;

            const widthRatio = containerWidth / w;
            const heightRatio = containerHeight / h;
            const scale = Math.min(widthRatio, heightRatio);

            const newCanvasWidth = w * scale;
            const newCanvasHeight = h * scale;

            canvas.clear();
            canvas.setWidth(newCanvasWidth);
            canvas.setHeight(newCanvasHeight);
            canvas.backgroundColor = "transparent";

            const croppedFabricImage = new fabric.Image(img, {
                left: 0,
                top: 0,
                selectable: false,
                evented: false,
            });

            croppedFabricImage.scaleX = scale;
            croppedFabricImage.scaleY = scale;

            canvas.add(croppedFabricImage);
            setOriginalImage(croppedFabricImage);

            const newState = canvas.toDataURL({
                multiplier: 1,
                format: "png",
                quality: 1,
            });
            setCanvasState(newState);

            // Update initialState to the state of the newly cropped image
            setInitialState(newState);

            canvas.renderAll();
        };

        img.src = croppedImageDataURL;

        canvas.remove(cropRect);
        setCropRect(null);
        setIsCropping(false);

        if (canvas && selectedMode === "magic" && magicSubMode === "brush") {
            canvas.isDrawingMode = true;
        }

        // Restore canvas to the state before cropping started
        if (preCropState) {
            restoreCanvasState(preCropState);
            setPreCropState(null); // Clear the stored state
        }
    };

    const cancelCrop = () => {
        if (canvas && cropRect) {
            canvas.remove(cropRect);
            canvas.renderAll();
        }
        setCropRect(null);
        setIsCropping(false);

        // Restore canvas to the state before cropping started
        if (preCropState) {
            restoreCanvasState(preCropState);
            setPreCropState(null); // Clear the stored state
        }

        if (canvas && selectedMode === "magic" && magicSubMode === "brush") {
            canvas.isDrawingMode = true;
        }
    };

    // Generate mask from SAM for use with existing API
    const generateSAMMaskAndSend = async (prompt: string, mode: MagicSubMode, useCase: UseCase) => {
        if (!canvas || !originalImage || !sam.samState.maskImg) return;
        setIsLoading(true);
        try {
            // Generate cutout from SAM mask
            const cutouts = sam.generateCutout(originalImage.getElement() as HTMLImageElement);
            if (!cutouts) {
                toast.error("Failed to generate mask from SAM selection");
                return;
            }

            const { annotatedMask, segmentedCutout } = cutouts;
            setAnnotatedImageUrl(segmentedCutout);

            console.log("Prompt Message:", prompt);
            console.log("Original Image URL:", originalImageUrl);
            console.log("SAM Annotated Mask:", annotatedMask);
            console.log("SAM Segmented Cutout:", segmentedCutout);

            const response = await editImage({
                base64EncodedOriginalImage: originalImageUrl!,
                base64EncodedMaskImage: annotatedMask,
                base64EncodedMaskColorImage: segmentedCutout!,
                promptMessage: prompt,
                mode: mode,
                useCase: useCase,
            });
            console.log("Response Image:", response);

            const editedImageUrl = typeof response === "string" ? response : (response as string);
            setOriginalImageUrl(editedImageUrl);
            restoreCanvasState(editedImageUrl);
            if (initialState) {
                clear();
                setCanvasState(initialState);
                setCanvasState(editedImageUrl);
            }
            toast.success("Image edited successfully with SAM");
            sam.reset(); // Reset SAM state after successful edit
        } catch (error) {
            console.error("Error generating or sending SAM mask:", error);
            toast.error("Error editing image with SAM. See console for details.");
        } finally {
            setIsLoading(false);
        }
    };

    const removeImage = () => {
        if (!canvas) return;
        
        // Complete reset for trash button - remove everything
        sam.resetAll(); // Complete SAM reset including tensor
        
        // Clear and dispose canvas
        canvas.clear();
        canvas.dispose();
        setCanvas(null);
        
        // Reset all image editor state completely
        setOriginalImage(null);
        setOriginalImageData(null);
        setOriginalImageUrl(null);
        setAnnotatedImageUrl(null);
        setInitialState(null);
        setCanvasState(null);
        setHasPainted(false);
        setIsCropping(false);
        setCropRect(null);
        setPreCropState(null);
        setIsLoading(false);
        setShowMaskPreview(false);
        
        // Clear history completely
        clear();
        
        // Reinitialize clean canvas
        setTimeout(() => {
            initializeCanvas();
        }, 100);
    };


    const handleSmartClick = (options: any) => {
        if (
            !canvas ||
            selectedMode !== "magic" ||
            magicSubMode !== "smart" ||
            !originalImage ||
            !sam.samState.tensor ||
            !options.e
        ) return;
        const pointer = canvas.getPointer(options.e as MouseEvent);
        const imgLeft = originalImage.left || 0;
        const imgTop = originalImage.top || 0;
        const imgScaleX = originalImage.scaleX || 1;
        const imgScaleY = originalImage.scaleY || 1;
        if (
            pointer.x >= imgLeft &&
            pointer.x <= imgLeft + originalImage.getScaledWidth() &&
            pointer.y >= imgTop &&
            pointer.y <= imgTop + originalImage.getScaledHeight()
        ) {
            const x = (pointer.x - imgLeft) / imgScaleX;
            const y = (pointer.y - imgTop) / imgScaleY;
            const clickType = sam.samState.isPositiveClick ? 1 : 0;
            clickHistoryRef.current.push({ x, y, clickType });
            redoClicksRef.current = [];
            
            const beforeClickState = canvas.toDataURL({ multiplier: 1, format: "png", quality: 1 });
            setCanvasState(beforeClickState);
            
            sam.handleClick(x, y, () => {
                setTimeout(() => {
                    const afterClickState = canvas.toDataURL({ multiplier: 1, format: "png", quality: 1 });
                    setCanvasState(afterClickState);
                }, 200);
            });
        }
    };

    return {
        canvasRef,
        canvasContainerRef,
        cursorCanvasRef,
        samMaskCanvasRef,
        canvas,
        originalImage,
        isLoading: isLoading || sam.samState.isLoading,
        showMaskPreview,
        originalImageUrl,
        annotatedImageUrl,
        handleFileUpload,
        brushSize,
        setBrushSize,
        undo: undoWithSAMReset,
        redo: redoWithSAMAndPaint,
        canUndo,
        canRedo,
        clearBrushStrokes,
        generateMaskAndSend,
        generateSAMMaskAndSend,
        generateRemoveBackground,
        downloadOriginalImage,
        hideMaskPreview,
        toggleMaskPreview,
        hasImage,
        applyFillColor,
        applyPattern,
        applyMaterial,
        applyFilter,
        resetImage,
        resetSAMAnnotations, 
        startCrop,
        confirmCrop,
        cancelCrop,
        isCropping,
        removeImage,
        hasPainted,
        testLog,
        // SAM functionality
        sam,
        handleSmartClick,
        hasSAMSelection: sam.samState.clicks.length > 0 && sam.samState.maskImg !== null,
        samState: sam.samState,
    } as const;
}
