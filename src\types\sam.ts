import * as ort from "onnxruntime-web";

export interface SAMModelScale {
  samScale: number;
  height: number;
  width: number;
}

export interface SAMClick {
  x: number;
  y: number;
  clickType: 0 | 1; // 0 for negative (remove), 1 for positive (add)
}

export interface SAMModelInput {
  x: number;
  y: number;
  clickType: number; // 0 for negative, 1 for positive, -1 for padding
}

export interface SAMModeData {
  clicks?: Array<SAMModelInput>;
  tensor: ort.Tensor;
  modelScale: SAMModelScale;
}

export interface SAMState {
  model: ort.InferenceSession | null;
  tensor: ort.Tensor | null;
  modelScale: SAMModelScale | null;
  clicks: SAMClick[];
  maskImg: HTMLImageElement | null;
  isLoading: boolean;
  error: string | null;
  isPositiveClick: boolean;
  isMaskInverted: boolean;
  hoverMaskImg?: HTMLImageElement | null;
  isProcessingHover?: boolean;
}

export interface SAMConfig {
  modelPath: string;
  embeddingEndpoint: string;
}